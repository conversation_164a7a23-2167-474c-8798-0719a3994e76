"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/use-audio-task.ts":
/*!*************************************!*\
  !*** ./src/hooks/use-audio-task.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAudioTask: () => (/* binding */ useAudioTask)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_use_live2d_expression__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/use-live2d-expression */ \"(app-pages-browser)/./src/hooks/use-live2d-expression.ts\");\n/* harmony import */ var _cubismsdksamples_lappdefine__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @cubismsdksamples/lappdefine */ \"(app-pages-browser)/./WebSDK/src/lappdefine.ts\");\n/* __next_internal_client_entry_do_not_use__ useAudioTask auto */ /* eslint-disable func-names */ /* eslint-disable no-underscore-dangle */ /* eslint-disable @typescript-eslint/ban-ts-comment */ \n\n\n/**\n * Custom hook for handling audio playback tasks with Live2D lip sync\n */ const useAudioTask = ()=>{\n    const { setExpression } = (0,_hooks_use_live2d_expression__WEBPACK_IMPORTED_MODULE_1__.useLive2DExpression)();\n    // Track current audio and model\n    const currentAudioRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const currentModelRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    /**\n   * Stop current audio playback and lip sync\n   */ const stopCurrentAudioAndLipSync = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioTask.useCallback[stopCurrentAudioAndLipSync]\": ()=>{\n            if (currentAudioRef.current) {\n                console.log('Stopping current audio and lip sync');\n                const audio = currentAudioRef.current;\n                audio.pause();\n                audio.src = '';\n                audio.load();\n                const model = currentModelRef.current;\n                if (model && model._wavFileHandler) {\n                    try {\n                        // Release PCM data to stop lip sync calculation in update()\n                        model._wavFileHandler.releasePcmData();\n                        console.log('Called _wavFileHandler.releasePcmData()');\n                        // Additional reset of state variables as fallback\n                        model._wavFileHandler._lastRms = 0.0;\n                        model._wavFileHandler._sampleOffset = 0;\n                        model._wavFileHandler._userTimeSeconds = 0.0;\n                        console.log('Also reset _lastRms, _sampleOffset, _userTimeSeconds as fallback');\n                    } catch (e) {\n                        console.error('Error stopping/resetting wavFileHandler:', e);\n                    }\n                } else if (model) {\n                    console.warn('Current model does not have _wavFileHandler to stop/reset.');\n                } else {\n                    console.log('No associated model found to stop lip sync.');\n                }\n                currentAudioRef.current = null;\n                currentModelRef.current = null;\n            } else {\n                console.log('No current audio playing to stop.');\n            }\n        }\n    }[\"useAudioTask.useCallback[stopCurrentAudioAndLipSync]\"], []);\n    /**\n   * Handle audio playback with Live2D lip sync\n   */ const handleAudioPlayback = (options)=>new Promise((resolve)=>{\n            const { audioBase64, expressions } = options;\n            try {\n                // Process audio if available\n                if (audioBase64) {\n                    var _window_getLive2DManager, _window, _window_getLAppAdapter, _window1;\n                    // Change the MIME type to audio/mp3 which is more widely supported\n                    const audioDataUrl = \"data:audio/wav;base64,\".concat(audioBase64);\n                    // Get Live2D manager and model\n                    const live2dManager = (_window_getLive2DManager = (_window = window).getLive2DManager) === null || _window_getLive2DManager === void 0 ? void 0 : _window_getLive2DManager.call(_window);\n                    if (!live2dManager) {\n                        console.error('Live2D manager not found');\n                        resolve();\n                        return;\n                    }\n                    const model = live2dManager.getModel(0);\n                    if (!model) {\n                        console.error('Live2D model not found at index 0');\n                        resolve();\n                        return;\n                    }\n                    console.log('Found model for audio playback');\n                    currentModelRef.current = model;\n                    if (!model._wavFileHandler) {\n                        console.warn('Model does not have _wavFileHandler for lip sync');\n                    } else {\n                        console.log('Model has _wavFileHandler available');\n                    }\n                    // Set expression if available\n                    const lappAdapter = (_window_getLAppAdapter = (_window1 = window).getLAppAdapter) === null || _window_getLAppAdapter === void 0 ? void 0 : _window_getLAppAdapter.call(_window1);\n                    if (lappAdapter && (expressions === null || expressions === void 0 ? void 0 : expressions[0]) !== undefined) {\n                        setExpression(expressions[0], lappAdapter, \"Set expression to: \".concat(expressions[0]));\n                    }\n                    // Start talk motion\n                    if (_cubismsdksamples_lappdefine__WEBPACK_IMPORTED_MODULE_2__ && _cubismsdksamples_lappdefine__WEBPACK_IMPORTED_MODULE_2__.PriorityNormal) {\n                        console.log(\"Starting random 'Talk' motion\");\n                        model.startRandomMotion(\"Talk\", _cubismsdksamples_lappdefine__WEBPACK_IMPORTED_MODULE_2__.PriorityNormal);\n                    } else {\n                        console.warn(\"LAppDefine.PriorityNormal not found - cannot start talk motion\");\n                    }\n                    // Setup audio element\n                    const audio = new Audio();\n                    currentAudioRef.current = audio;\n                    let isFinished = false;\n                    const cleanup = ()=>{\n                        if (currentAudioRef.current === audio) {\n                            currentAudioRef.current = null;\n                            currentModelRef.current = null;\n                        }\n                        if (!isFinished) {\n                            isFinished = true;\n                            resolve();\n                        }\n                    };\n                    // Enhance lip sync sensitivity\n                    const lipSyncScale = 2.0;\n                    audio.addEventListener('canplaythrough', ()=>{\n                        // Check for interruption before playback\n                        if (currentAudioRef.current !== audio) {\n                            console.warn('Audio playback cancelled due to new audio');\n                            cleanup();\n                            return;\n                        }\n                        console.log('Starting audio playback with lip sync');\n                        audio.play().catch((err)=>{\n                            console.error(\"Audio play error:\", err);\n                            cleanup();\n                        });\n                        // Setup lip sync\n                        if (model._wavFileHandler) {\n                            if (!model._wavFileHandler._initialized) {\n                                console.log('Applying enhanced lip sync');\n                                model._wavFileHandler._initialized = true;\n                                const originalUpdate = model._wavFileHandler.update.bind(model._wavFileHandler);\n                                model._wavFileHandler.update = function(deltaTimeSeconds) {\n                                    const result = originalUpdate(deltaTimeSeconds);\n                                    // @ts-ignore\n                                    this._lastRms = Math.min(2.0, this._lastRms * lipSyncScale);\n                                    return result;\n                                };\n                            }\n                            if (currentAudioRef.current === audio) {\n                                model._wavFileHandler.start(audioDataUrl);\n                            } else {\n                                console.warn('WavFileHandler start skipped - audio was stopped');\n                            }\n                        }\n                    });\n                    audio.addEventListener('ended', ()=>{\n                        console.log(\"Audio playback completed\");\n                        cleanup();\n                    });\n                    audio.addEventListener('error', (error)=>{\n                        var _audioElement_error, _audioElement_error1;\n                        console.error(\"Audio playback error:\", error);\n                        // Add more detailed error information\n                        const audioElement = error.target;\n                        console.error(\"Audio error code:\", (_audioElement_error = audioElement.error) === null || _audioElement_error === void 0 ? void 0 : _audioElement_error.code);\n                        console.error(\"Audio error message:\", (_audioElement_error1 = audioElement.error) === null || _audioElement_error1 === void 0 ? void 0 : _audioElement_error1.message);\n                        cleanup();\n                    });\n                    // Set the source after adding event listeners\n                    audio.src = audioDataUrl;\n                    audio.load();\n                } else {\n                    resolve();\n                }\n            } catch (error) {\n                console.error('Audio playback setup error:', error);\n                currentAudioRef.current = null;\n                currentModelRef.current = null;\n                resolve();\n            }\n        });\n    /**\n   * Add a new audio task to the queue\n   */ const addAudioTask = async (options)=>{\n        console.log(\"Playing audio with expressions: \".concat(options.expressions));\n        await handleAudioPlayback(options);\n    };\n    return {\n        addAudioTask,\n        stopCurrentAudioAndLipSync\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/use-audio-task.ts\n"));

/***/ })

});