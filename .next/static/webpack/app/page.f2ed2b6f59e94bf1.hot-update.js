"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/use-audio-task.ts":
/*!*************************************!*\
  !*** ./src/hooks/use-audio-task.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAudioTask: () => (/* binding */ useAudioTask)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_use_live2d_expression__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/use-live2d-expression */ \"(app-pages-browser)/./src/hooks/use-live2d-expression.ts\");\n/* harmony import */ var _cubismsdksamples_lappdefine__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @cubismsdksamples/lappdefine */ \"(app-pages-browser)/./WebSDK/src/lappdefine.ts\");\n/* __next_internal_client_entry_do_not_use__ useAudioTask auto */ /* eslint-disable func-names */ /* eslint-disable no-underscore-dangle */ /* eslint-disable @typescript-eslint/ban-ts-comment */ \n\n\n/**\n * Custom hook for handling audio playback tasks with Live2D lip sync\n */ const useAudioTask = ()=>{\n    const { setExpression } = (0,_hooks_use_live2d_expression__WEBPACK_IMPORTED_MODULE_1__.useLive2DExpression)();\n    // Track current audio and model\n    const currentAudioRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const currentModelRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    /**\n   * Stop current audio playback and lip sync\n   */ const stopCurrentAudioAndLipSync = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAudioTask.useCallback[stopCurrentAudioAndLipSync]\": ()=>{\n            if (currentAudioRef.current) {\n                console.log('Stopping current audio and lip sync');\n                const audio = currentAudioRef.current;\n                audio.pause();\n                audio.src = '';\n                audio.load();\n                const model = currentModelRef.current;\n                if (model && model._wavFileHandler) {\n                    try {\n                        // Release PCM data to stop lip sync calculation in update()\n                        model._wavFileHandler.releasePcmData();\n                        console.log('Called _wavFileHandler.releasePcmData()');\n                        // Additional reset of state variables as fallback\n                        model._wavFileHandler._lastRms = 0.0;\n                        model._wavFileHandler._sampleOffset = 0;\n                        model._wavFileHandler._userTimeSeconds = 0.0;\n                        console.log('Also reset _lastRms, _sampleOffset, _userTimeSeconds as fallback');\n                    } catch (e) {\n                        console.error('Error stopping/resetting wavFileHandler:', e);\n                    }\n                } else if (model) {\n                    console.warn('Current model does not have _wavFileHandler to stop/reset.');\n                } else {\n                    console.log('No associated model found to stop lip sync.');\n                }\n                currentAudioRef.current = null;\n                currentModelRef.current = null;\n            } else {\n                console.log('No current audio playing to stop.');\n            }\n        }\n    }[\"useAudioTask.useCallback[stopCurrentAudioAndLipSync]\"], []);\n    /**\n   * Handle audio playback with Live2D lip sync\n   */ const handleAudioPlayback = (options)=>new Promise((resolve)=>{\n            const { audioBase64, expressions } = options;\n            try {\n                // Process audio if available\n                if (audioBase64) {\n                    var _window_getLive2DManager, _window, _window_getLAppAdapter, _window1;\n                    // Change the MIME type to audio/mp3 which is more widely supported\n                    const audioDataUrl = \"data:audio/wav;base64,\".concat(audioBase64);\n                    // Get Live2D manager and model\n                    const live2dManager = (_window_getLive2DManager = (_window = window).getLive2DManager) === null || _window_getLive2DManager === void 0 ? void 0 : _window_getLive2DManager.call(_window);\n                    if (!live2dManager) {\n                        console.error('Live2D manager not found');\n                        resolve();\n                        return;\n                    }\n                    const model = live2dManager.getModel(0);\n                    if (!model) {\n                        console.error('Live2D model not found at index 0');\n                        resolve();\n                        return;\n                    }\n                    console.log('Found model for audio playback');\n                    currentModelRef.current = model;\n                    if (!model._wavFileHandler) {\n                        console.warn('Model does not have _wavFileHandler for lip sync');\n                    } else {\n                        console.log('Model has _wavFileHandler available');\n                    }\n                    // Set expression if available\n                    const lappAdapter = (_window_getLAppAdapter = (_window1 = window).getLAppAdapter) === null || _window_getLAppAdapter === void 0 ? void 0 : _window_getLAppAdapter.call(_window1);\n                    if (lappAdapter && (expressions === null || expressions === void 0 ? void 0 : expressions[0]) !== undefined) {\n                        setExpression(expressions[0], lappAdapter, \"Set expression to: \".concat(expressions[0]));\n                    }\n                    // Start talk motion\n                    if (_cubismsdksamples_lappdefine__WEBPACK_IMPORTED_MODULE_2__ && _cubismsdksamples_lappdefine__WEBPACK_IMPORTED_MODULE_2__.PriorityNormal) {\n                        console.log(\"Starting random 'Talk' motion\");\n                        model.startRandomMotion(\"Talk\", _cubismsdksamples_lappdefine__WEBPACK_IMPORTED_MODULE_2__.PriorityNormal);\n                    } else {\n                        console.warn(\"LAppDefine.PriorityNormal not found - cannot start talk motion\");\n                    }\n                    // Setup audio element\n                    const audio = new Audio(audioDataUrl);\n                    currentAudioRef.current = audio;\n                    let isFinished = false;\n                    const cleanup = ()=>{\n                        if (currentAudioRef.current === audio) {\n                            currentAudioRef.current = null;\n                            currentModelRef.current = null;\n                        }\n                        if (!isFinished) {\n                            isFinished = true;\n                            resolve();\n                        }\n                    };\n                    // Enhance lip sync sensitivity\n                    const lipSyncScale = 2.0;\n                    audio.addEventListener('canplaythrough', ()=>{\n                        // Check for interruption before playback\n                        if (currentAudioRef.current !== audio) {\n                            console.warn('Audio playback cancelled due to new audio');\n                            cleanup();\n                            return;\n                        }\n                        console.log('Starting audio playback with lip sync');\n                        audio.play().catch((err)=>{\n                            console.error(\"Audio play error:\", err);\n                            cleanup();\n                        });\n                        // Setup lip sync\n                        if (model._wavFileHandler) {\n                            if (!model._wavFileHandler._initialized) {\n                                console.log('Applying enhanced lip sync');\n                                model._wavFileHandler._initialized = true;\n                                const originalUpdate = model._wavFileHandler.update.bind(model._wavFileHandler);\n                                model._wavFileHandler.update = function(deltaTimeSeconds) {\n                                    const result = originalUpdate(deltaTimeSeconds);\n                                    // @ts-ignore\n                                    this._lastRms = Math.min(2.0, this._lastRms * lipSyncScale);\n                                    return result;\n                                };\n                            }\n                            if (currentAudioRef.current === audio) {\n                                model._wavFileHandler.start(audioDataUrl);\n                            } else {\n                                console.warn('WavFileHandler start skipped - audio was stopped');\n                            }\n                        }\n                    });\n                    audio.addEventListener('ended', ()=>{\n                        console.log(\"Audio playback completed\");\n                        cleanup();\n                    });\n                    audio.addEventListener('error', (error)=>{\n                        console.error(\"Audio playback error:\", error);\n                        // Add more detailed error logging\n                        console.error(\"Audio error details:\", error.target);\n                        cleanup();\n                    });\n                    audio.load();\n                } else {\n                    resolve();\n                }\n            } catch (error) {\n                console.error('Audio playback setup error:', error);\n                currentAudioRef.current = null;\n                currentModelRef.current = null;\n                resolve();\n            }\n        });\n    /**\n   * Add a new audio task to the queue\n   */ const addAudioTask = async (options)=>{\n        console.log(\"Playing audio with expressions: \".concat(options.expressions));\n        await handleAudioPlayback(options);\n    };\n    return {\n        addAudioTask,\n        stopCurrentAudioAndLipSync\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/use-audio-task.ts\n"));

/***/ })

});